# ConversationalBIM Thesis Writing Automation

This repository contains automated scripts for writing the complete ConversationalBIM bachelor thesis using the OpenCode AI writer system.

## 📁 Files Overview

- **`write_complete_thesis.sh`** - Main automation script that orchestrates the entire thesis writing process
- **`thesis_manager.sh`** - Utility script for individual thesis management tasks
- **`THESIS_AUTOMATION_README.md`** - This documentation file

## 🚀 Quick Start

### Complete Thesis Writing (Automated)

To write the entire thesis automatically:

```bash
# Run complete thesis writing process (non-interactive)
./write_complete_thesis.sh

# Run with confirmation prompts at each phase
./write_complete_thesis.sh --interactive
```

### Individual Task Management

For specific tasks, use the thesis manager:

```bash
# Check current status
./thesis_manager.sh status

# Research a specific topic
./thesis_manager.sh research "natural language interfaces"

# Write a specific chapter
./thesis_manager.sh write "Chapter 1: Introduction"

# Review content
./thesis_manager.sh review "Chapter 2"

# Compile thesis
./thesis_manager.sh compile
```

## 📋 Main Script Options

### `write_complete_thesis.sh` Options

```bash
# Complete automated thesis writing
./write_complete_thesis.sh

# Interactive mode with confirmations
./write_complete_thesis.sh --interactive

# Write only a specific chapter (1-7)
./write_complete_thesis.sh --chapter 3

# Run only the research phase
./write_complete_thesis.sh --research-only

# Show help
./write_complete_thesis.sh --help
```

### `thesis_manager.sh` Commands

```bash
# Status and progress
./thesis_manager.sh status          # Check thesis status
./thesis_manager.sh progress        # Detailed progress report
./thesis_manager.sh logs            # Show recent log files

# Content creation
./thesis_manager.sh research "topic"    # Research specific topic
./thesis_manager.sh write "section"     # Write section/chapter
./thesis_manager.sh review "content"    # Review content

# Document management
./thesis_manager.sh compile         # Compile thesis
./thesis_manager.sh references      # Manage citations
./thesis_manager.sh backup          # Create backup
./thesis_manager.sh clean           # Clean temp files
```

## 🔄 Complete Thesis Writing Process

The main script follows this **improved workflow** with proper file persistence:

### Phase 1: Initial Setup and Status Check

- Checks current thesis status using thesis-coordinator
- Identifies existing content and gaps
- Creates TODO list for remaining work

### Phase 2: Complete Thesis Writing (Research → Write → Review per Chapter)

**NEW APPROACH**: Each chapter follows the proper workflow:

1. **Research Phase for Chapter**: Targeted research for specific chapter topics
2. **Writing Phase**: Immediate writing using fresh research materials
3. **Review Phase**: Quality check and feedback for the written chapter
4. **Compilation Check**: LaTeX compilation to catch errors early

**Chapters Processed:**

1. **Chapter 1**: Introduction and Problem Statement
2. **Chapter 2**: Fundamentals and Related Work
3. **Chapter 3**: System Design and Architecture
4. **Chapter 4**: Implementation
5. **Chapter 5**: Evaluation and Usability Study
6. **Chapter 6**: Results and Discussion
7. **Chapter 7**: Conclusion and Outlook

**Key Improvements:**

- ✅ **File Persistence**: All research, writing, and reviews saved to files
- ✅ **Targeted Research**: Research specific to each chapter's needs
- ✅ **Immediate Application**: Write chapters using fresh research
- ✅ **Quality Control**: Review each chapter before proceeding
- ✅ **Error Prevention**: Compile after each chapter to catch issues early

### Phase 3: Quality Assurance

- Comprehensive thesis review across all chapters
- Citation validation and bibliography formatting
- Final compilation and error resolution

### Phase 4: Final Status and Completion

- Final status assessment and completion verification
- Delivery of complete, compiled thesis

## 📊 Monitoring and Logging

### Log Files

All script executions create detailed log files:

- Format: `thesis_writing_log_YYYYMMDD_HHMMSS.log`
- Location: Project root directory
- Contains: All command outputs, timestamps, success/error status

### Progress Tracking

```bash
# View detailed progress
./thesis_manager.sh progress

# Check recent logs
./thesis_manager.sh logs

# View latest log in real-time
tail -f $(ls -t thesis_writing_log_*.log | head -1)
```

## 🛠️ Thesis Structure

The scripts work with this file organization:

```
/home/<USER>/dev/ai/bachelorarbeit/
├── thesis/
│   ├── writing/
│   │   └── thesis.tex          # Main thesis document
│   └── .context/               # Research materials (reference only)
│       ├── research/           # Research reports
│       ├── papers/             # Downloaded PDFs
│       ├── references/         # Bibliography files
│       ├── outlines/           # Chapter outlines
│       ├── reviews/            # Review reports
│       └── notes/              # General notes
├── .opencode/                  # AI agent configurations
├── write_complete_thesis.sh    # Main automation script
├── thesis_manager.sh           # Management utility
└── thesis_writing_log_*.log    # Execution logs
```

## ⚙️ OpenCode Integration - NEW ORCHESTRATOR APPROACH

The scripts now use an **improved orchestrator-based approach** with the OpenCode AI writer system:

### Single Entry Point Architecture

- **thesis-coordinator** - ONLY agent used directly by scripts
- All other agents are sub-agents called by thesis-coordinator
- No more direct command usage - everything goes through orchestrator
- Ensures proper workflow and file persistence

### Agent Hierarchy

**Primary Orchestrator:**

- **thesis-coordinator** - Main orchestration, creates TODO lists, manages workflow

**Sub-agents (called by thesis-coordinator):**

- **@literature-research** - Academic paper discovery and analysis
- **@technical-research** - Technical documentation research
- **@writer** - Content creation and academic writing
- **@reviewer** - Quality assurance and consistency checking
- **@reference-manager** - Citation and bibliography management
- **@latex-formatter** - LaTeX formatting and compilation

### Key Improvements

- ✅ **File Persistence**: All sub-agents MUST save work to files
- ✅ **Proper Workflow**: Research → Write → Review per section
- ✅ **Clear Reporting**: Each sub-agent reports what files were created/modified
- ✅ **TODO Management**: Orchestrator creates and follows explicit task lists
- ✅ **Single Entry Point**: All requests go through thesis-coordinator

### Custom Tools Added

- **download_arxiv** - Downloads arXiv papers as PDFs to thesis/.context/papers/
- **batch_download** - Downloads multiple papers in batch

### Message Format (No More Commands)

Instead of commands like `/research "topic"`, scripts now send natural language messages:

```
"Research the topic: [topic]. Use @literature-research and @technical-research
to find relevant papers. Save all findings to files."
```

## 🔧 Troubleshooting

### Common Issues

**Script Permission Errors**

```bash
chmod +x write_complete_thesis.sh
chmod +x thesis_manager.sh
```

**OpenCode Not Found**

- Ensure OpenCode is installed and in PATH
- Verify you're in the correct directory: `/home/<USER>/dev/ai/bachelorarbeit`

**Compilation Errors**

```bash
# Check LaTeX installation
./thesis_manager.sh compile

# Clean temporary files
./thesis_manager.sh clean
```

**Log File Issues**

```bash
# View recent logs
./thesis_manager.sh logs

# Check specific log
tail -100 thesis_writing_log_YYYYMMDD_HHMMSS.log
```

### Recovery Options

**Resume from Interruption**

- The script can be re-run safely - it will check existing content
- Use `./thesis_manager.sh status` to assess current state
- Use individual commands to complete specific tasks

**Backup and Recovery**

```bash
# Create backup
./thesis_manager.sh backup

# List backups
ls -la backups/

# Restore from backup (manual)
tar -xzf backups/thesis_backup_YYYYMMDD_HHMMSS.tar.gz
```

## 📈 Expected Timeline

**Complete Thesis Writing Process**: 6-12 hours

- Research Phase: 2-4 hours
- Chapter Writing: 3-6 hours
- Quality Assurance: 1-2 hours

**Individual Chapters**: 20-45 minutes each
**Research Topics**: 5-15 minutes each
**Reviews**: 10-20 minutes each

_Note: Times depend on system performance, network speed, and AI response times_

## 🎯 Success Indicators

The process is successful when:

- ✅ All 7 chapters are written and reviewed
- ✅ Comprehensive research materials are collected
- ✅ Citations and bibliography are properly formatted
- ✅ LaTeX compilation produces clean PDF output
- ✅ Final status check shows 100% completion
- ✅ Thesis meets academic standards for submission

## 📞 Support

For issues or questions:

1. Check the log files for detailed error information
2. Use `./thesis_manager.sh progress` for current status
3. Run individual commands to isolate problems
4. Refer to the OpenCode documentation in `.opencode/` directory

The automation system is designed to handle the complete thesis writing process while maintaining high academic standards and providing comprehensive documentation of all activities.
