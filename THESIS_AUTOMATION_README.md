# ConversationalBIM Thesis Writing Automation

This repository contains automated scripts for writing the complete ConversationalBIM bachelor thesis using the OpenCode AI writer system.

## 📁 Files Overview

- **`write_complete_thesis.sh`** - Main automation script that orchestrates the entire thesis writing process
- **`thesis_manager.sh`** - Utility script for individual thesis management tasks
- **`THESIS_AUTOMATION_README.md`** - This documentation file

## 🚀 Quick Start

### Complete Thesis Writing (Automated)

To write the entire thesis automatically:

```bash
# Run complete thesis writing process (non-interactive)
./write_complete_thesis.sh

# Run with confirmation prompts at each phase
./write_complete_thesis.sh --interactive
```

### Individual Task Management

For specific tasks, use the thesis manager:

```bash
# Check current status
./thesis_manager.sh status

# Research a specific topic
./thesis_manager.sh research "natural language interfaces"

# Write a specific chapter
./thesis_manager.sh write "Chapter 1: Introduction"

# Review content
./thesis_manager.sh review "Chapter 2"

# Compile thesis
./thesis_manager.sh compile
```

## 📋 Main Script Options

### `write_complete_thesis.sh` Options

```bash
# Complete automated thesis writing
./write_complete_thesis.sh

# Interactive mode with confirmations
./write_complete_thesis.sh --interactive

# Write only a specific chapter (1-7)
./write_complete_thesis.sh --chapter 3

# Run only the research phase
./write_complete_thesis.sh --research-only

# Show help
./write_complete_thesis.sh --help
```

### `thesis_manager.sh` Commands

```bash
# Status and progress
./thesis_manager.sh status          # Check thesis status
./thesis_manager.sh progress        # Detailed progress report
./thesis_manager.sh logs            # Show recent log files

# Content creation
./thesis_manager.sh research "topic"    # Research specific topic
./thesis_manager.sh write "section"     # Write section/chapter
./thesis_manager.sh review "content"    # Review content

# Document management
./thesis_manager.sh compile         # Compile thesis
./thesis_manager.sh references      # Manage citations
./thesis_manager.sh backup          # Create backup
./thesis_manager.sh clean           # Clean temp files
```

## 🔄 Complete Thesis Writing Process

The main script follows this comprehensive workflow:

### Phase 1: Initial Setup and Status Check
- Checks current thesis status
- Identifies existing content and gaps
- Prepares for research phase

### Phase 2: Research Foundation
Conducts comprehensive research on all key topics:
- Conversational AI interfaces for technical domains
- Building Information Modeling (BIM) and graph databases
- Large Language Models and tool-augmented agents
- Keystroke Level Model (KLM) evaluation methodology
- Natural language query interfaces and SPARQL generation
- Retrieval Augmented Generation (RAG) and document processing
- FastAPI microservices architecture
- Kafka integration and real-time data synchronization
- GraphDB and semantic web technologies
- Vector databases and semantic search
- Document processing and PDF extraction
- Human-computer interaction evaluation methods
- Building information management systems
- Linked Building Data (LBD) and semantic technologies

### Phase 3: Chapter Writing
Writes all chapters in order:
1. **Chapter 1**: Introduction and Problem Statement
2. **Chapter 2**: Fundamentals and Related Work
3. **Chapter 3**: System Design and Architecture
4. **Chapter 4**: Implementation
5. **Chapter 5**: Evaluation and Usability Study
6. **Chapter 6**: Results and Discussion
7. **Chapter 7**: Conclusion and Outlook

Each chapter includes:
- Content writing using research materials
- Quality review and feedback
- LaTeX compilation and error checking

### Phase 4: Quality Assurance
- Comprehensive thesis review
- Citation validation and formatting
- Bibliography management
- Final compilation checks

### Phase 5: Final Status and Completion
- Final status assessment
- Complete thesis compilation
- Delivery of finished thesis

## 📊 Monitoring and Logging

### Log Files
All script executions create detailed log files:
- Format: `thesis_writing_log_YYYYMMDD_HHMMSS.log`
- Location: Project root directory
- Contains: All command outputs, timestamps, success/error status

### Progress Tracking
```bash
# View detailed progress
./thesis_manager.sh progress

# Check recent logs
./thesis_manager.sh logs

# View latest log in real-time
tail -f $(ls -t thesis_writing_log_*.log | head -1)
```

## 🛠️ Thesis Structure

The scripts work with this file organization:

```
/home/<USER>/dev/ai/bachelorarbeit/
├── thesis/
│   ├── writing/
│   │   └── thesis.tex          # Main thesis document
│   └── .context/               # Research materials (reference only)
│       ├── research/           # Research reports
│       ├── papers/             # Downloaded PDFs
│       ├── references/         # Bibliography files
│       ├── outlines/           # Chapter outlines
│       ├── reviews/            # Review reports
│       └── notes/              # General notes
├── .opencode/                  # AI agent configurations
├── write_complete_thesis.sh    # Main automation script
├── thesis_manager.sh           # Management utility
└── thesis_writing_log_*.log    # Execution logs
```

## ⚙️ OpenCode Integration

The scripts use the OpenCode AI writer system with these agents:

### Primary Agents
- **thesis-coordinator** - Main orchestration and project management
- **research** - Academic and technical research coordination
- **writer** - Content creation and academic writing
- **reviewer** - Quality assurance and consistency checking

### Sub-agents
- **@literature-research** - Academic paper discovery and analysis
- **@technical-research** - Technical documentation research
- **@reference-manager** - Citation and bibliography management
- **@latex-formatter** - LaTeX formatting and compilation

### Commands Used
- `/status` - Progress tracking and status checks
- `/research "topic"` - Comprehensive topic research
- `/write "section"` - Chapter/section writing
- `/review "content"` - Quality review and feedback
- `/compile` - LaTeX compilation and formatting
- `/references "task"` - Reference management

## 🔧 Troubleshooting

### Common Issues

**Script Permission Errors**
```bash
chmod +x write_complete_thesis.sh
chmod +x thesis_manager.sh
```

**OpenCode Not Found**
- Ensure OpenCode is installed and in PATH
- Verify you're in the correct directory: `/home/<USER>/dev/ai/bachelorarbeit`

**Compilation Errors**
```bash
# Check LaTeX installation
./thesis_manager.sh compile

# Clean temporary files
./thesis_manager.sh clean
```

**Log File Issues**
```bash
# View recent logs
./thesis_manager.sh logs

# Check specific log
tail -100 thesis_writing_log_YYYYMMDD_HHMMSS.log
```

### Recovery Options

**Resume from Interruption**
- The script can be re-run safely - it will check existing content
- Use `./thesis_manager.sh status` to assess current state
- Use individual commands to complete specific tasks

**Backup and Recovery**
```bash
# Create backup
./thesis_manager.sh backup

# List backups
ls -la backups/

# Restore from backup (manual)
tar -xzf backups/thesis_backup_YYYYMMDD_HHMMSS.tar.gz
```

## 📈 Expected Timeline

**Complete Thesis Writing Process**: 6-12 hours
- Research Phase: 2-4 hours
- Chapter Writing: 3-6 hours  
- Quality Assurance: 1-2 hours

**Individual Chapters**: 20-45 minutes each
**Research Topics**: 5-15 minutes each
**Reviews**: 10-20 minutes each

*Note: Times depend on system performance, network speed, and AI response times*

## 🎯 Success Indicators

The process is successful when:
- ✅ All 7 chapters are written and reviewed
- ✅ Comprehensive research materials are collected
- ✅ Citations and bibliography are properly formatted
- ✅ LaTeX compilation produces clean PDF output
- ✅ Final status check shows 100% completion
- ✅ Thesis meets academic standards for submission

## 📞 Support

For issues or questions:
1. Check the log files for detailed error information
2. Use `./thesis_manager.sh progress` for current status
3. Run individual commands to isolate problems
4. Refer to the OpenCode documentation in `.opencode/` directory

The automation system is designed to handle the complete thesis writing process while maintaining high academic standards and providing comprehensive documentation of all activities.
