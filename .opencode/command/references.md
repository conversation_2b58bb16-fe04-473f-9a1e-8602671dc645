---
description: Manage references and citations for the ConversationalBIM thesis
agent: reference-manager
model: github-copilot/gpt-4.1
---

# Reference Management: $ARGUMENTS

Manage citations, bibliography, and reference integrity for: **$ARGUMENTS**

## Critical: Thesis Folder Structure

**IMPORTANT - Understanding thesis folder structure:**

- `thesis/.context/` - Contains research materials, papers, notes, references, and all supporting documentation. This is for **CONTEXT and REFERENCE only**.
- `thesis/writing/` - Contains the actual thesis document (`thesis.tex`) that is being written. This is where the **main LaTeX thesis content lives**.

**When managing references:**

- Store bibliography files (.bib) and reference materials in `thesis/.context/references/`
- Update citations in the main thesis document located at `thesis/writing/thesis.tex`
- Downloaded papers and reference PDFs go in `thesis/.context/papers/`
- The main thesis bibliography should be accessible from `thesis/writing/` but stored in context for organization
- Always distinguish between reference storage (context) and thesis content (writing)

## Reference Management Tasks

1. **Bibliography Maintenance**: Update and validate BibTeX database
2. **Citation Integration**: Process new citations and format properly
3. **Reference Validation**: Verify source accessibility and accuracy
4. **Consistency Checking**: Ensure uniform citation style throughout
5. **Duplicate Resolution**: Identify and resolve duplicate references

## Specific Actions Based on Request

- **Add References**: Process new references from research agents
- **Validate Citations**: Check existing citations for accuracy and completeness
- **Format Bibliography**: Apply proper academic citation formatting
- **Check Duplicates**: Scan for and resolve duplicate bibliography entries
- **Verify URLs**: Validate web resource accessibility and create archives

## Reference Processing Workflow

1. **Source Analysis**: Extract complete bibliographic information
2. **BibTeX Creation**: Create properly formatted BibTeX entries
3. **Citation Key Generation**: Generate consistent citation keys
4. **Quality Validation**: Verify completeness and accuracy
5. **Database Integration**: Add to master bibliography database

## Citation Standards

- **Computer Science Format**: Use appropriate CS academic citation style
- **Consistency**: Maintain uniform formatting across all references
- **Completeness**: Ensure all required fields are present
- **Accuracy**: Verify publication details and accessibility
- **Attribution**: Proper attribution of all sources and ideas

## Quality Assurance

- **Cross-Reference Check**: Every in-text citation has bibliography entry
- **Format Validation**: Consistent BibTeX formatting throughout
- **Source Verification**: Confirm source accessibility and accuracy
- **Duplicate Detection**: No duplicate entries in bibliography
- **Archive Creation**: Create archived versions of web resources

## Collaboration

- Support @research with bibliography for discovered sources
- Assist @writer with proper citation integration
- Coordinate with @latex-formatter for bibliography compilation
- Report issues to @reviewer for resolution

## Output Requirements

- Updated and validated bibliography database
- Properly formatted BibTeX entries
- Consistent citation keys following naming conventions
- Quality report on reference integrity
- Archive links for web resources

Maintain the highest standards of academic citation integrity while supporting the thesis development process efficiently.
