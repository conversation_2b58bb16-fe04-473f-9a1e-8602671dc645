---
description: Check thesis progress and provide status update on completion and next steps
agent: thesis-coordinator
model: github-copilot/gpt-4.1
---

# Thesis Progress Status Check

Provide comprehensive status update on ConversationalBIM thesis progress and identify next steps.

## Critical: Thesis Folder Structure

**IMPORTANT - Understanding thesis folder structure:**

- `thesis/.context/` - Contains research materials, papers, notes, references, and all supporting documentation. This is for **CONTEXT and REFERENCE only**.
- `thesis/writing/` - Contains the actual thesis document (`thesis.tex`) that is being written. This is where the **main LaTeX thesis content lives**.

**When checking status:**

- Review progress on the main thesis document at `thesis/writing/thesis.tex`
- Check supporting research materials in `thesis/.context/` for completeness
- Assess both the actual thesis content and the supporting research materials
- Report status of thesis writing separate from context material collection
- Always distinguish between thesis content progress and research material availability

## Progress Assessment Areas

1. **Chapter Completion Status**: Review progress on all thesis chapters
2. **Research Coverage**: Assess completeness of research activities
3. **Quality Standards**: Evaluate current quality level and remaining issues
4. **Timeline Analysis**: Compare progress against deadlines and milestones
5. **Resource Requirements**: Identify needed resources and support

## Status Review Process

1. **File System Scan**: Review all thesis files and content status
2. **Agent Activity Review**: Check recent activity from all specialized agents
3. **Quality Assessment**: Evaluate current work against academic standards
4. **Gap Analysis**: Identify missing content and incomplete sections
5. **Priority Setting**: Determine highest-priority next steps

## Deliverable Status Check

- **Chapter 1: Introduction** - Status and completion level
- **Chapter 2: Fundamentals** - Research coverage and writing progress
- **Chapter 3: System Design** - Technical documentation completeness
- **Chapter 4: Implementation** - Development documentation status
- **Chapter 5: Evaluation** - Methodology and study design progress
- **Chapter 6: Results** - Analysis and findings documentation
- **Chapter 7: Conclusion** - Summary and future work status

## Quality Metrics

- **Content Quality**: Academic writing standards compliance
- **Technical Accuracy**: Correctness of technical content
- **Citation Integrity**: Reference completeness and accuracy
- **Formatting Standards**: LaTeX formatting and presentation quality
- **Consistency**: Uniformity across all thesis components

## Risk Assessment

- **Time Constraints**: Deadline pressure and remaining work
- **Quality Issues**: Known problems requiring attention
- **Resource Limitations**: Missing information or access issues
- **Technical Challenges**: Compilation or formatting problems

## Next Steps Planning

1. **Immediate Priorities**: Most urgent tasks requiring attention
2. **Short-term Goals**: Tasks for next 1-2 weeks
3. **Medium-term Objectives**: Goals for next month
4. **Risk Mitigation**: Actions to address identified risks
5. **Quality Improvements**: Opportunities for enhancement

## Coordination Requirements

- **Research Needs**: Outstanding research tasks
- **Writing Priorities**: Content creation priorities
- **Review Schedule**: Quality review and feedback cycles
- **Technical Support**: LaTeX formatting and compilation needs

## Output Format

Provide clear, actionable status report with:

- Current completion percentage by chapter
- Critical path items requiring immediate attention
- Resource and support needs
- Timeline for remaining work
- Risk factors and mitigation strategies

Focus on actionable insights that help drive the thesis to successful completion within deadline constraints while maintaining high academic standards.
