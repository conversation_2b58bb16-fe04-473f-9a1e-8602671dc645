---
description: Write a complete chapter or section of the ConversationalBIM thesis
agent: writer
model: github-copilot/gpt-4.1
---

# Write Chapter/Section: $ARGUMENTS

You need to write the following chapter or section: **$ARGUMENTS**

## Critical: Thesis Folder Structure

**IMPORTANT - Understanding thesis folder structure:**

- `thesis/.context/` - Contains research materials, papers, notes, references, and all supporting documentation. This is for **CONTEXT and REFERENCE only**.
- `thesis/writing/` - Contains the actual thesis document (`thesis.tex`) that is being written. This is where the **main LaTeX thesis content lives**.

**When writing thesis content:**

- Write content for the main thesis document located at `thesis/writing/thesis.tex`
- Use research materials in `thesis/.context/research/` for reference and supporting information
- Access references from `thesis/.context/references/` for citations
- Always write in the actual thesis document, not in context directories
- Use context materials for research support but create original thesis content in the writing directory

## Writing Requirements

1. **Academic Standards**: Follow formal academic writing style for Computer Science thesis
2. **LaTeX Format**: Write content in proper LaTeX format with appropriate markup
3. **Citation Integration**: Include proper citations and references throughout
4. **Structural Coherence**: Ensure logical flow and clear organization
5. **Technical Accuracy**: Maintain high technical accuracy and precision

## Pre-Writing Process

1. Review existing research materials in `thesis/.context/research/`
2. Check available references in `thesis/.context/references/`
3. Outline the section structure before writing
4. Identify key points and supporting evidence
5. Plan figure and table integration

## Content Guidelines

- **ConversationalBIM Focus**: Keep content focused on thesis objectives and contributions
- **Original Analysis**: Provide original insights and analysis, not just summarization
- **Evidence-Based**: Support all claims with appropriate citations and evidence
- **Clear Explanations**: Explain complex concepts clearly for academic audience
- **Proper Attribution**: Distinguish clearly between existing work and original contributions

## Collaboration

- Work with @reference-manager for proper citations and bibliography
- Coordinate with @latex-formatter for complex formatting requirements
- Submit to @reviewer for quality check before finalizing
- Integrate feedback from @thesis-coordinator for strategic alignment

## Output Requirements

- Complete LaTeX-formatted chapter or section
- Proper cross-references to figures, tables, equations
- Integrated citations with proper formatting
- Professional academic writing meeting thesis standards
- Content ready for integration into main thesis document

Write comprehensive, well-researched content that advances the thesis narrative and meets the highest academic standards.
