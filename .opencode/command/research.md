---
description: Start comprehensive research on a specific topic for the ConversationalBIM thesis
agent: research
model: github-copilot/gpt-4.1
---

# Research Command: $ARGUMENTS

You need to conduct comprehensive research on the topic: **$ARGUMENTS**

## Critical: Thesis Folder Structure

**IMPORTANT - Understanding thesis folder structure:**

- `thesis/.context/` - Contains research materials, papers, notes, references, and all supporting documentation. This is for **CONTEXT and REFERENCE only**.
- `thesis/writing/` - Contains the actual thesis document (`thesis.tex`) that is being written. This is where the **main LaTeX thesis content lives**.

**When conducting research:**

- Save all research findings and notes to `thesis/.context/research/`
- Store downloaded papers in `thesis/.context/papers/`
- The research you conduct will inform the thesis content at `thesis/writing/thesis.tex`
- Always distinguish between collecting research materials (context) and writing the actual thesis (writing)

## Research Objectives

1. **Literature Discovery**: Find the most relevant and high-quality academic papers on this topic
2. **Technical Analysis**: Understand current approaches, methodologies, and implementations
3. **Gap Identification**: Identify research gaps that ConversationalBIM can address
4. **Synthesis**: Create comprehensive research summary ready for thesis integration

## Research Process

1. Use @literature-research to find academic papers using arXiv and Google Scholar
2. Use @technical-research to find technical documentation and implementation examples
3. Analyze findings for relevance to ConversationalBIM objectives
4. Create detailed research report with key findings and implications
5. Identify specific citations and references for thesis use

## Deliverables

- Comprehensive research report on the topic
- List of key papers with summaries and relevance assessments
- Technical implementation insights and best practices
- Identified research gaps and opportunities
- Bibliography entries ready for integration

## Context

This research is for the ConversationalBIM bachelor thesis, which focuses on AI-powered conversational interfaces for building information queries, with evaluation using Keystroke-Level Model methodology.

Focus on finding research that supports the thesis narrative and provides solid academic foundation for the work.
