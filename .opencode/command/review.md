---
description: Conduct comprehensive quality review of thesis content for academic standards and consistency
agent: reviewer
model: github-copilot/gpt-4.1
---

# Review Command: $ARGUMENTS

You need to conduct a comprehensive quality review of: **$ARGUMENTS**

## Critical: Thesis Folder Structure

**IMPORTANT - Understanding thesis folder structure:**

- `thesis/.context/` - Contains research materials, papers, notes, references, and all supporting documentation. This is for **CONTEXT and REFERENCE only**.
- `thesis/writing/` - Contains the actual thesis document (`thesis.tex`) that is being written. This is where the **main LaTeX thesis content lives**.

**When conducting reviews:**

- Review the main thesis document located at `thesis/writing/thesis.tex`
- Use materials in `thesis/.context/` for reference verification and fact-checking only
- Save review notes and feedback to `thesis/.context/reviews/`
- Focus review efforts on the actual thesis content, not the supporting research materials
- Always distinguish between reviewing reference materials vs. reviewing the actual thesis

## Review Scope

This review should cover the specified content (chapter, section, or entire thesis) for:

1. **Academic Writing Quality**: Style, tone, clarity, and scholarly standards
2. **Technical Accuracy**: Correctness of technical content and concepts
3. **Consistency**: Terminology, style, and formatting consistency
4. **Citation Integrity**: Proper citations, attribution, and bibliography
5. **Structural Coherence**: Logical flow and organization

## Review Process

1. **Content Analysis**: Deep reading for quality, accuracy, and coherence
2. **Style Assessment**: Academic writing style and tone evaluation
3. **Technical Validation**: Verify technical claims and implementations
4. **Consistency Check**: Terminology and formatting consistency across content
5. **Citation Verification**: Validate citations and reference accuracy
6. **Structural Review**: Assess logical organization and flow

## Review Deliverables

1. **Executive Summary**: High-level assessment of content quality and readiness
2. **Detailed Feedback**: Specific comments and suggestions for improvement
3. **Issue Categorization**: Problems classified by severity and type
4. **Improvement Plan**: Prioritized recommendations for enhancement
5. **Quality Rating**: Overall assessment and submission readiness

## Review Standards

- **Academic Rigor**: Ensure content meets high academic standards
- **Professional Quality**: Publication-quality writing and presentation
- **Technical Accuracy**: All technical content must be correct and current
- **Consistency**: Uniform style and terminology throughout
- **Citation Compliance**: Proper academic citation standards

## Review Categories

- **Critical Issues**: Must be fixed before submission
- **Important Improvements**: Should be addressed for quality enhancement
- **Minor Suggestions**: Optional improvements for polish
- **Positive Elements**: Strengths to maintain and build upon

## Collaboration

- Coordinate with @thesis-coordinator for priority setting
- Work with @writer to implement improvements
- Collaborate with @reference-manager for citation issues
- Support @latex-formatter with formatting concerns

Provide thorough, constructive review that helps elevate the thesis to the highest academic standards while maintaining focus on ConversationalBIM objectives and contributions.
