import { tool } from "@opencode-ai/plugin";
import * as fs from "fs";
import * as path from "path";
import * as https from "https";

export default tool({
  description:
    "Download arXiv papers as PDF files to thesis/.context/papers/ directory (custom reliable implementation)",
  args: {
    arxiv_id: tool.schema
      .string()
      .describe(
        "arXiv paper ID (e.g., '2301.12345' or 'https://arxiv.org/abs/2301.12345')"
      ),
    filename: tool.schema
      .string()
      .optional()
      .describe("Optional custom filename (without extension)"),
  },
  async execute(args, context) {
    try {
      // Extract arXiv ID from URL if provided
      let arxivId = args.arxiv_id;
      if (arxivId.includes("arxiv.org")) {
        const match = arxivId.match(
          /(?:abs\/|pdf\/)([0-9]{4}\.[0-9]{4,5}(?:v[0-9]+)?)/
        );
        if (match) {
          arxivId = match[1];
        } else {
          return `Error: Could not extract arXiv ID from URL: ${args.arxiv_id}`;
        }
      }

      // Validate arXiv ID format
      if (!/^[0-9]{4}\.[0-9]{4,5}(?:v[0-9]+)?$/.test(arxivId)) {
        return `Error: Invalid arXiv ID format: ${arxivId}. Expected format: YYMM.NNNNN or YYMM.NNNNNvN`;
      }

      // Determine filename
      const filename = args.filename
        ? `${args.filename}.pdf`
        : `arxiv_${arxivId.replace(/[^a-zA-Z0-9]/g, "_")}.pdf`;

      // Ensure papers directory exists
      const papersDir = path.join(
        process.cwd(),
        "thesis",
        ".context",
        "papers"
      );
      if (!fs.existsSync(papersDir)) {
        fs.mkdirSync(papersDir, { recursive: true });
      }

      const filePath = path.join(papersDir, filename);

      // Check if file already exists
      if (fs.existsSync(filePath)) {
        return `Paper already exists: ${filePath}. arXiv ID: ${arxivId}`;
      }

      // Download URL for arXiv PDF
      const downloadUrl = `https://arxiv.org/pdf/${arxivId}.pdf`;

      // Download the file
      const downloadResult = await new Promise<string>((resolve, reject) => {
        const file = fs.createWriteStream(filePath);

        https
          .get(downloadUrl, (response) => {
            // Check if the response is successful
            if (response.statusCode !== 200) {
              fs.unlinkSync(filePath); // Clean up partial file
              reject(
                new Error(
                  `HTTP ${response.statusCode}: ${response.statusMessage}`
                )
              );
              return;
            }

            // Check content type
            const contentType = response.headers["content-type"];
            if (!contentType || !contentType.includes("application/pdf")) {
              fs.unlinkSync(filePath); // Clean up partial file
              reject(
                new Error(`Invalid content type: ${contentType}. Expected PDF.`)
              );
              return;
            }

            response.pipe(file);

            file.on("finish", () => {
              file.close();

              // Verify file size
              const stats = fs.statSync(filePath);
              if (stats.size < 1000) {
                // Less than 1KB is likely an error page
                fs.unlinkSync(filePath);
                reject(
                  new Error(
                    "Downloaded file is too small, likely an error page"
                  )
                );
                return;
              }

              resolve(
                `Successfully downloaded arXiv paper ${arxivId} to: ${filePath} (${Math.round(
                  stats.size / 1024
                )} KB)`
              );
            });

            file.on("error", (err) => {
              fs.unlinkSync(filePath); // Clean up partial file
              reject(err);
            });
          })
          .on("error", (err) => {
            if (fs.existsSync(filePath)) {
              fs.unlinkSync(filePath); // Clean up partial file
            }
            reject(err);
          });
      });

      return downloadResult;
    } catch (error) {
      return `Error downloading arXiv paper: ${
        error instanceof Error ? error.message : String(error)
      }`;
    }
  },
});

export const batch_download_arxiv = tool({
  description:
    "Download multiple arXiv papers in batch to thesis/.context/papers/",
  args: {
    arxiv_ids: tool.schema
      .array(tool.schema.string())
      .describe("Array of arXiv paper IDs or URLs"),
    prefix: tool.schema
      .string()
      .optional()
      .describe("Optional filename prefix for all downloads"),
  },
  async execute(args, context) {
    const results: string[] = [];

    for (let i = 0; i < args.arxiv_ids.length; i++) {
      const arxivId = args.arxiv_ids[i];
      const filename = args.prefix ? `${args.prefix}_${i + 1}` : undefined;

      try {
        // Use the main download function
        const downloadTool = (await import("./arxiv_downloader")).default;
        const result = await downloadTool.execute(
          { arxiv_id: arxivId, filename },
          context
        );
        results.push(`${i + 1}. ${result}`);
      } catch (error) {
        results.push(
          `${i + 1}. Error: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }

    return `Batch download completed:\n${results.join("\n")}`;
  },
});
