# ConversationalBIM Bachelor Thesis Rules

## Critical: Thesis Folder Structure

**IMPORTANT - All agents must understand this distinction:**

- `thesis/.context/` - Contains research materials, papers, notes, references, and all supporting documentation. This is for **CONTEXT and REFERENCE only**.
- `thesis/writing/` - Contains the actual thesis document (`thesis.tex`) that is being written. This is where the **main LaTeX thesis content lives**.

**File Organization Rules:**

- The main thesis document being authored is located at `thesis/writing/thesis.tex`
- All research materials, papers, and notes go in `thesis/.context/` subdirectories
- Always distinguish between supporting materials (context) and the actual thesis being written (writing)
- When agents work on "the thesis," they should target `thesis/writing/thesis.tex`
- Context materials are for reference and research support only

## Academic Writing Standards

### Formal Academic Style

- Use formal, professional academic language appropriate for a Computer Science bachelor thesis
- Maintain objective, scholarly tone throughout all content
- Use present tense for general principles and established facts
- Use past tense for completed work and specific studies
- Prefer active voice while maintaining appropriate academic distance
- Use first person plural ("we") when describing your own contributions

### Technical Precision

- Define all technical terms on first use
- Use consistent terminology throughout the thesis
- Provide precise, accurate descriptions of technical concepts
- Include sufficient detail for reproducibility
- Cite authoritative sources for technical claims
- Avoid ambiguous language or imprecise statements

### Citation and Attribution Standards

- Cite all sources using proper academic citation format
- Every factual claim must be supported by appropriate citations
- Distinguish clearly between your original work and existing research
- Use citations to support arguments, not replace them
- Acknowledge all prior work that influenced your approach
- Include page numbers for specific claims or direct quotes

## ConversationalBIM Thesis Structure Requirements

### Chapter Organization

- **Chapter 1: Introduction** - Problem statement, research questions, hypothesis, contributions
- **Chapter 2: Fundamentals** - Background, related work, theoretical foundations
- **Chapter 3: System Design** - Architecture, design decisions, technical approach
- **Chapter 4: Implementation** - Development process, technical details, system realization
- **Chapter 5: Evaluation** - Methodology, experimental setup, usability study design
- **Chapter 6: Results** - Findings, analysis, discussion of results
- **Chapter 7: Conclusion** - Summary, contributions, future work, implications

### Required Components

- Title page with proper institutional formatting
- Abstract in both German and English (maximum 300 words each)
- Table of Contents, List of Figures, List of Tables
- Acknowledgments section
- Complete bibliography with proper academic citations
- Appendices for supplementary material

### Content Flow Requirements

- Each chapter should build logically on previous chapters
- Clear transitions between sections and chapters
- Consistent narrative thread connecting all parts
- Proper cross-referencing to figures, tables, equations, and sections
- Balance between background material and original contributions

## LaTeX Formatting Standards

### Document Structure

- Use professional document class appropriate for thesis
- Implement proper page layout with appropriate margins
- Use consistent typography throughout the document
- Apply proper spacing for equations, figures, and text
- Maintain professional appearance suitable for academic submission

### Figure and Table Standards

- All figures must be high-resolution and professionally presented
- Include descriptive captions that explain the content
- Use consistent style across all visual elements
- Reference all figures and tables in the text
- Position figures and tables appropriately relative to text

### Code and Technical Content

- Format code listings with proper syntax highlighting
- Use monospace fonts for code, file names, and technical identifiers
- Include line numbers for longer code examples
- Provide clear comments explaining complex code segments
- Format algorithms using appropriate pseudocode conventions

## Research and Content Standards

### Literature Review Requirements

- Include comprehensive coverage of relevant research areas
- Focus on recent work (last 5 years) while including seminal papers
- Critically analyze existing work, don't just summarize
- Clearly identify research gaps that your work addresses
- Balance breadth of coverage with depth of analysis

### Technical Implementation Standards

- Provide sufficient technical detail for understanding and reproduction
- Include architectural diagrams and system overviews
- Document key design decisions and their rationale
- Include relevant code examples and implementation details
- Address scalability, performance, and security considerations

### Evaluation and Results Standards

- Use appropriate evaluation methodologies (KLM for interface efficiency)
- Provide clear description of experimental setup and procedures
- Present results objectively with appropriate statistical analysis
- Discuss limitations and threats to validity honestly
- Support conclusions with evidence from evaluation results

## Quality Assurance Requirements

### Content Quality

- Every paragraph should have a clear purpose and contribute to the chapter's objectives
- Eliminate redundant or irrelevant content
- Ensure logical flow from sentence to sentence and paragraph to paragraph
- Maintain appropriate level of detail for intended audience
- Provide clear explanations of complex concepts

### Consistency Requirements

- Use consistent terminology throughout the thesis
- Maintain consistent writing style and tone
- Apply formatting rules consistently across all content
- Ensure consistent citation style and format
- Use consistent naming conventions for files, figures, and references

### Accuracy and Verification

- Verify all factual claims and technical assertions
- Double-check all citations and references
- Ensure all cross-references are accurate and functional
- Validate all URLs and online resources
- Proofread for grammar, spelling, and typographical errors

## Collaboration Guidelines

### Agent Coordination

- @thesis-coordinator manages overall project coordination
- @research conducts academic and technical research
- @writer creates academic content following these standards
- @reviewer ensures quality and consistency
- Sub-agents handle specialized tasks within their expertise areas

### File Organization

**CRITICAL - All agents must understand this structure:**

- **Main thesis content**: `thesis/writing/thesis.tex` - This is the actual thesis document being written
- **Supporting materials**: `thesis/.context/` - This contains reference materials only:
  - Research materials organized in `thesis/.context/research/`
  - Paper PDFs stored in `thesis/.context/papers/`
  - Reference management in `thesis/.context/references/`
  - Review notes documented in `thesis/.context/reviews/`

**Key Rule**: Always distinguish between the thesis document being authored (writing/) and supporting reference materials (context/)

### Communication Standards

- All agents must follow these rules consistently
- Report deviations from standards immediately
- Document any necessary exceptions with justification
- Maintain clear communication about progress and issues
- Coordinate to avoid conflicting work or redundancy

## Thesis-Specific Domain Requirements

### ConversationalBIM Context

- Maintain focus on conversational AI interfaces for building information
- Emphasize the innovation of natural language interfaces replacing graph UIs
- Consistently reference the KLM evaluation methodology
- Highlight integration with ZIM project and real-world applicability
- Balance technical depth with accessibility to interdisciplinary audience

### Building Information Modeling Domain

- Use correct BIM terminology and concepts
- Reference appropriate industry standards (IFC, buildingSMART)
- Acknowledge construction industry context and requirements
- Properly explain RDF, SPARQL, and semantic web concepts
- Connect technical implementation to practical building management needs

### Evaluation Methodology

- Apply Keystroke-Level Model (KLM) methodology correctly
- Provide clear justification for evaluation approach
- Include both quantitative and qualitative evaluation components
- Address threats to validity and study limitations
- Compare results fairly with existing approaches

## Emergency Protocols

### Time Pressure Management

- Prioritize core thesis requirements over optional enhancements
- Focus on quality over quantity when time is limited
- Maintain academic standards even under deadline pressure
- Clearly document any compromises made due to time constraints
- Ensure minimum quality standards are always met

### Quality vs. Speed Trade-offs

- Never compromise on academic integrity or citation standards
- Maintain technical accuracy even when simplifying explanations
- Preserve logical structure and flow even in rushed sections
- Keep formatting professional even with time constraints
- Document areas that need further development post-submission

These rules provide the foundation for producing a high-quality, academically rigorous bachelor thesis on ConversationalBIM. All agents must follow these standards consistently to ensure the final thesis meets the highest academic and professional standards.
