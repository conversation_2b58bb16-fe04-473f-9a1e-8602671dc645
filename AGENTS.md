# ConversationalBIM Bachelor Thesis Project

This is a Computer Science bachelor thesis project focused on **ConversationalBIM: An AI-Powered Conversational Interface for Building Information Queries**. The project demonstrates how conversational AI can improve efficiency in accessing building information compared to traditional graph-based interfaces.

## Project Overview

### Thesis Objective

Develop and evaluate ConversationalBIM, a service that integrates with the ZIM project PortfolioBIM to provide an AI-powered conversational interface for querying building data. The system listens to Kafka topics, synchronizes RDF data about buildings, processes associated documents, and provides a natural language API for querying this information.

### Key Innovation

Instead of users navigating complex graph UIs, table views, or writing SPARQL queries manually, they can ask natural language questions and receive comprehensive answers that combine structured graph data with relevant document content.

### Technical Stack

- **AI Framework**: PydanticAI with tool-augmented LLMs
- **API Layer**: FastAPI with async processing
- **Data Storage**: GraphDB for RDF data, Qdrant for vector search
- **Integration**: Kafka for real-time data synchronization
- **Document Processing**: Docling for PDF extraction and processing
- **Evaluation**: Keystroke-Level Model (KLM) methodology

## Project Structure

### Main Components

- `thesis/` - Thesis directory containing:
  - `thesis/.context/` - Research materials, papers, notes, and supporting documentation (CONTEXT ONLY)
  - `thesis/writing/` - Contains `thesis.tex` - the actual thesis document being written
- `backend/` - ConversationalBIM implementation (Python/FastAPI)
- `.opencode/` - OpenCode configuration for AI-assisted thesis writing

### Important: Thesis Folder Structure

**Critical distinction for all AI agents:**

- `thesis/.context/` - Contains research materials, papers, notes, references, and all supporting documentation. This is for CONTEXT and REFERENCE only.
- `thesis/writing/` - Contains the actual thesis document (`thesis.tex`) that is being written. This is where the main LaTeX thesis content lives.

All agents must understand this distinction to avoid confusion between reference materials and the actual thesis document being authored.

### Thesis Structure

1. **Introduction and Problem Statement** - Current challenges and research gap
2. **Fundamentals and Related Work** - Literature review and technical background
3. **System Design and Architecture** - ConversationalBIM architecture and design
4. **Implementation** - Development methodology and technical implementation
5. **Evaluation and Usability Study** - KLM-based evaluation methodology
6. **Results and Discussion** - Findings, analysis, and implications
7. **Conclusion and Outlook** - Contributions and future research directions

## Research Focus Areas

### Building Information Modeling (BIM)

- Traditional BIM limitations (IFC schema rigidity, proprietary formats)
- Linked Building Data (LBD) advantages and adoption challenges
- Current graph database interaction methods and their limitations

### Conversational AI for Technical Domains

- Large Language Model applications in information retrieval
- Tool-augmented LLM architectures and agent-based systems
- Retrieval-Augmented Generation (RAG) for document integration
- Multi-modal reasoning across structured and unstructured data

### Human-Computer Interaction Evaluation

- Keystroke-Level Model (KLM) for interface efficiency measurement
- Comparative evaluation methodologies for traditional vs. conversational interfaces
- Usability testing protocols for technical domain applications

## Development Approach

### Design Science Research Methodology

- Iterative prototype development with continuous evaluation
- Problem identification and solution design
- Artifact development and demonstration
- Rigorous evaluation through KLM methodology
- Communication of results and contributions

### Quality Standards

- Academic rigor with comprehensive literature review
- Technical accuracy in implementation descriptions
- Proper citation and attribution of all sources
- Professional LaTeX formatting and presentation
- Reproducible research methodology and evaluation

## Integration Context

### ZIM Project Integration

- Collaborative research with HTW Dresden University, ekkodale GmbH, Metabuild GmbH
- Portfolio Information Management for building data
- Excel files (IBPDI standard) → graph visualization → conversational access
- Real-world application context with 25+ years BIM experience

### Industry Relevance

- Addresses practical challenges in building data management
- Provides accessible interface for non-technical stakeholders
- Reduces training requirements for building information systems
- Enables faster decision-making in building operations and maintenance

## AI Agent Usage Guidelines

When working on this thesis project:

### Content Focus

- Maintain focus on conversational AI interfaces for building information systems
- Emphasize practical applications and industry relevance
- Balance technical depth with accessibility to interdisciplinary audience
- Clearly articulate novel contributions and research significance

### Research Standards

- Prioritize recent work (last 5 years) while including seminal papers
- Use authoritative sources from top-tier venues (ACM, IEEE, etc.)
- Critically analyze existing work rather than just summarizing
- Identify research gaps that ConversationalBIM addresses

### Technical Accuracy

- Ensure correct usage of BIM, RDF, SPARQL, and AI terminology
- Validate all technical claims and implementation details
- Provide sufficient detail for understanding and reproduction
- Address scalability, performance, and practical considerations

### Academic Writing

- Follow formal Computer Science academic writing standards
- Use proper LaTeX formatting with professional presentation
- Maintain consistent terminology and citation style throughout
- Support all claims with appropriate evidence and citations

This thesis represents a significant contribution to both the Building Information Modeling and Conversational AI research communities, demonstrating practical applications of advanced AI technologies in the construction industry.
