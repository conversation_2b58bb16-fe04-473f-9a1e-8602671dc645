#!/bin/bash

# ConversationalBIM Thesis Writing Automation Script
# This script orchestrates the complete thesis writing process using OpenCode AI agents
# Author: AI Assistant
# Date: $(date +%Y-%m-%d)

set -e  # Exit on any error

# Configuration
THESIS_DIR="/home/<USER>/dev/ai/bachelorarbeit"
LOG_FILE="$THESIS_DIR/thesis_writing_log_$(date +%Y%m%d_%H%M%S).log"
OPENCODE_CMD="opencode --agent thesis-coordinator run"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${BLUE}[$timestamp]${NC} $message" | tee -a "$LOG_FILE"
}

log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${GREEN}[$timestamp] ✓${NC} $message" | tee -a "$LOG_FILE"
}

log_warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${YELLOW}[$timestamp] ⚠${NC} $message" | tee -a "$LOG_FILE"
}

log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${RED}[$timestamp] ✗${NC} $message" | tee -a "$LOG_FILE"
}

# Function to execute OpenCode command and log output
execute_opencode() {
    local message="$1"
    local description="$2"

    log "Starting: $description"
    log "Message: $message"

    cd "$THESIS_DIR"

    # Execute command and capture output - using thesis-coordinator as single entry point
    if output=$(eval "$OPENCODE_CMD \"$message\"" 2>&1); then
        log_success "Completed: $description"
        echo "$output" >> "$LOG_FILE"
        echo "---" >> "$LOG_FILE"
        return 0
    else
        log_error "Failed: $description"
        echo "ERROR OUTPUT:" >> "$LOG_FILE"
        echo "$output" >> "$LOG_FILE"
        echo "---" >> "$LOG_FILE"
        return 1
    fi
}

# Function to wait for user confirmation (optional)
wait_for_confirmation() {
    local message="$1"
    if [[ "${INTERACTIVE:-false}" == "true" ]]; then
        echo -e "${YELLOW}$message${NC}"
        read -p "Press Enter to continue or Ctrl+C to abort..."
    fi
}

# Main thesis writing workflow
main() {
    log "=== ConversationalBIM Thesis Writing Process Started ==="
    log "Working directory: $THESIS_DIR"
    log "Log file: $LOG_FILE"
    
    cd "$THESIS_DIR"
    
    # Phase 1: Initial Status Check and Setup
    log "=== PHASE 1: INITIAL SETUP AND STATUS CHECK ==="

    execute_opencode "/status" "Initial thesis status check"
    wait_for_confirmation "Phase 1 completed. Ready to proceed with thesis writing?"

    # Phase 2: Complete Thesis Writing (Research → Write → Review per chapter)
    log "=== PHASE 2: COMPLETE THESIS WRITING ==="

    # Define chapters with their research needs
    declare -A chapters=(
        ["Chapter 1: Introduction and Problem Statement"]="conversational AI interfaces technical domains, Building Information Modeling challenges, research gaps identification"
        ["Chapter 2: Fundamentals and Related Work"]="Large Language Models tool augmented agents, natural language database interfaces, Keystroke Level Model evaluation, Retrieval Augmented Generation"
        ["Chapter 3: System Design and Architecture"]="PydanticAI agent architecture, FastAPI microservices, Kafka integration, GraphDB RDF storage, Qdrant vector search"
        ["Chapter 4: Implementation"]="Python implementation patterns, document processing Docling, real-time data synchronization, API development"
        ["Chapter 5: Evaluation and Usability Study"]="Keystroke Level Model methodology, human computer interaction evaluation, usability testing protocols"
        ["Chapter 6: Results and Discussion"]="evaluation results analysis, efficiency measurements, user feedback analysis, system performance"
        ["Chapter 7: Conclusion and Outlook"]="research contributions summary, future work directions, practical implications"
    )

    # Process each chapter: Research → Write → Review
    for chapter in "Chapter 1: Introduction and Problem Statement" "Chapter 2: Fundamentals and Related Work" "Chapter 3: System Design and Architecture" "Chapter 4: Implementation" "Chapter 5: Evaluation and Usability Study" "Chapter 6: Results and Discussion" "Chapter 7: Conclusion and Outlook"; do
        log "--- Processing $chapter ---"

        # Research for this specific chapter
        research_topics="${chapters[$chapter]}"
        execute_opencode "Research the following topics for $chapter: $research_topics. Use @literature-research and @technical-research to find relevant papers and documentation. Save all findings to files." "Research for $chapter"

        # Write the chapter using research
        execute_opencode "Write $chapter using the research materials you just gathered. Use @writer to create comprehensive academic content and save to thesis/writing/thesis.tex." "Write $chapter"

        # Review the chapter
        execute_opencode "Review $chapter that was just written. Use @reviewer to check quality, consistency, and academic standards. Save review notes to files." "Review $chapter"

        wait_for_confirmation "$chapter completed. Ready to continue?"
    done
    
    # Phase 3: Quality Assurance and Final Review
    log "=== PHASE 3: QUALITY ASSURANCE ==="

    execute_opencode "Conduct comprehensive review of the entire thesis. Use @reviewer to check overall quality, consistency, and academic standards across all chapters. Save detailed review notes." "Comprehensive thesis review"

    execute_opencode "Manage all references and citations. Use @reference-manager to validate all citations, check for duplicates, and format bibliography according to academic standards." "Reference management"

    execute_opencode "Compile the complete thesis document. Use @latex-formatter to handle LaTeX compilation, resolve any errors, and generate final PDF." "Final thesis compilation"

    # Phase 4: Final Status and Completion
    log "=== PHASE 4: FINAL STATUS AND COMPLETION ==="

    execute_opencode "/status" "Final thesis status check"
    
    log_success "=== THESIS WRITING PROCESS COMPLETED ==="
    log "Complete log available at: $LOG_FILE"
    log "Thesis document location: $THESIS_DIR/thesis/writing/thesis.tex"
    log "PDF output should be available at: $THESIS_DIR/thesis/writing/thesis.pdf"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --interactive     Run in interactive mode with confirmation prompts"
    echo "  --resume PHASE    Resume from specific phase (1-5)"
    echo "  --chapter N       Write only specific chapter (1-7)"
    echo "  --research-only   Run only the research phase"
    echo "  --help           Show this help message"
    echo ""
    echo "Phases:"
    echo "  1 - Initial Setup and Status Check"
    echo "  2 - Research Foundation"
    echo "  3 - Chapter Writing"
    echo "  4 - Quality Assurance"
    echo "  5 - Final Status and Completion"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run complete thesis writing process"
    echo "  $0 --interactive      # Run with confirmation prompts"
    echo "  $0 --resume 3         # Resume from chapter writing phase"
    echo "  $0 --chapter 2        # Write only Chapter 2"
    echo "  $0 --research-only    # Run only research phase"
}

# Function to write specific chapter
write_chapter() {
    local chapter_num="$1"
    local chapter_titles=(
        ""  # Index 0 unused
        "Chapter 1: Introduction and Problem Statement"
        "Chapter 2: Fundamentals and Related Work"
        "Chapter 3: System Design and Architecture"
        "Chapter 4: Implementation"
        "Chapter 5: Evaluation and Usability Study"
        "Chapter 6: Results and Discussion"
        "Chapter 7: Conclusion and Outlook"
    )

    local chapter_research=(
        ""  # Index 0 unused
        "conversational AI interfaces technical domains, Building Information Modeling challenges, research gaps identification"
        "Large Language Models tool augmented agents, natural language database interfaces, Keystroke Level Model evaluation, Retrieval Augmented Generation"
        "PydanticAI agent architecture, FastAPI microservices, Kafka integration, GraphDB RDF storage, Qdrant vector search"
        "Python implementation patterns, document processing Docling, real-time data synchronization, API development"
        "Keystroke Level Model methodology, human computer interaction evaluation, usability testing protocols"
        "evaluation results analysis, efficiency measurements, user feedback analysis, system performance"
        "research contributions summary, future work directions, practical implications"
    )

    if [[ $chapter_num -lt 1 || $chapter_num -gt 7 ]]; then
        log_error "Invalid chapter number: $chapter_num. Must be 1-7."
        exit 1
    fi

    local chapter_title="${chapter_titles[$chapter_num]}"
    local research_topics="${chapter_research[$chapter_num]}"

    log "=== WRITING SINGLE CHAPTER: $chapter_title ==="

    # Research → Write → Review workflow
    execute_opencode "Research the following topics for $chapter_title: $research_topics. Use @literature-research and @technical-research to find relevant papers and documentation. Save all findings to files." "Research for $chapter_title"

    execute_opencode "Write $chapter_title using the research materials you just gathered. Use @writer to create comprehensive academic content and save to thesis/writing/thesis.tex." "Write $chapter_title"

    execute_opencode "Review $chapter_title that was just written. Use @reviewer to check quality, consistency, and academic standards. Save review notes to files." "Review $chapter_title"

    execute_opencode "Compile the thesis document to check for LaTeX errors. Use @latex-formatter to handle compilation and resolve any issues." "Compile after $chapter_title"

    log_success "Chapter $chapter_num completed successfully"
}

# Function to run only research phase
research_only() {
    log "=== RESEARCH-ONLY MODE ==="

    execute_opencode "/status" "Initial status check"

    # Comprehensive research request
    research_message="Conduct comprehensive research for the ConversationalBIM thesis. Research the following key areas and save all findings to files:

1. Conversational AI interfaces for technical domains
2. Building Information Modeling (BIM) and graph databases with RDF
3. Large Language Models and tool-augmented agents (especially PydanticAI)
4. Keystroke Level Model (KLM) interface evaluation methodology
5. Natural language query interfaces and SPARQL generation
6. Retrieval Augmented Generation (RAG) and document processing
7. FastAPI microservices architecture for building data
8. Kafka integration and real-time data synchronization
9. GraphDB and triple store semantic web technologies
10. Qdrant vector database and semantic search
11. Docling document processing and PDF extraction
12. Human-computer interaction evaluation methods
13. Conversational interfaces usability studies
14. Building information management systems
15. Linked Building Data (LBD) and semantic technologies

Use @literature-research and @technical-research to find relevant papers and documentation. Download papers using the download_arxiv tool. Save all research findings to thesis/.context/research/ with descriptive filenames."

    execute_opencode "$research_message" "Comprehensive research phase"

    execute_opencode "/status" "Post-research status check"
    log_success "Research phase completed successfully"
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Parse command line arguments
    INTERACTIVE=false
    RESUME_PHASE=""
    CHAPTER_ONLY=""
    RESEARCH_ONLY=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --interactive)
                INTERACTIVE=true
                shift
                ;;
            --resume)
                RESUME_PHASE="$2"
                shift 2
                ;;
            --chapter)
                CHAPTER_ONLY="$2"
                shift 2
                ;;
            --research-only)
                RESEARCH_ONLY=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    export INTERACTIVE

    # Trap to handle interruption
    trap 'log_error "Script interrupted by user"; exit 1' INT

    # Execute based on options
    if [[ "$RESEARCH_ONLY" == "true" ]]; then
        research_only
    elif [[ -n "$CHAPTER_ONLY" ]]; then
        write_chapter "$CHAPTER_ONLY"
    elif [[ -n "$RESUME_PHASE" ]]; then
        log "Resume functionality not yet implemented. Running full process."
        main
    else
        main
    fi
fi
