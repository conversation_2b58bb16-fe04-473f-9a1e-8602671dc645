#!/bin/bash

# ConversationalBIM Thesis Writing Automation Script
# This script orchestrates the complete thesis writing process using OpenCode AI agents
# Author: AI Assistant
# Date: $(date +%Y-%m-%d)

set -e  # Exit on any error

# Configuration
THESIS_DIR="/home/<USER>/dev/ai/bachelorarbeit"
LOG_FILE="$THESIS_DIR/thesis_writing_log_$(date +%Y%m%d_%H%M%S).log"
OPENCODE_CMD="opencode --agent thesis-coordinator run"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${BLUE}[$timestamp]${NC} $message" | tee -a "$LOG_FILE"
}

log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${GREEN}[$timestamp] ✓${NC} $message" | tee -a "$LOG_FILE"
}

log_warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${YELLOW}[$timestamp] ⚠${NC} $message" | tee -a "$LOG_FILE"
}

log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${RED}[$timestamp] ✗${NC} $message" | tee -a "$LOG_FILE"
}

# Function to execute OpenCode command and log output
execute_opencode() {
    local command="$1"
    local description="$2"
    
    log "Starting: $description"
    log "Command: $command"
    
    cd "$THESIS_DIR"
    
    # Execute command and capture output
    if output=$(eval "$OPENCODE_CMD \"$command\"" 2>&1); then
        log_success "Completed: $description"
        echo "$output" >> "$LOG_FILE"
        echo "---" >> "$LOG_FILE"
        return 0
    else
        log_error "Failed: $description"
        echo "ERROR OUTPUT:" >> "$LOG_FILE"
        echo "$output" >> "$LOG_FILE"
        echo "---" >> "$LOG_FILE"
        return 1
    fi
}

# Function to wait for user confirmation (optional)
wait_for_confirmation() {
    local message="$1"
    if [[ "${INTERACTIVE:-false}" == "true" ]]; then
        echo -e "${YELLOW}$message${NC}"
        read -p "Press Enter to continue or Ctrl+C to abort..."
    fi
}

# Main thesis writing workflow
main() {
    log "=== ConversationalBIM Thesis Writing Process Started ==="
    log "Working directory: $THESIS_DIR"
    log "Log file: $LOG_FILE"
    
    cd "$THESIS_DIR"
    
    # Phase 1: Initial Status Check and Setup
    log "=== PHASE 1: INITIAL SETUP AND STATUS CHECK ==="
    
    execute_opencode "/status" "Initial thesis status check"
    wait_for_confirmation "Phase 1 completed. Ready to proceed with research phase?"
    
    # Phase 2: Comprehensive Research Foundation
    log "=== PHASE 2: RESEARCH FOUNDATION ==="
    
    # Core research topics based on thesis outline
    research_topics=(
        "conversational AI interfaces for technical domains"
        "Building Information Modeling BIM graph databases RDF"
        "Large Language Models tool augmented agents PydanticAI"
        "Keystroke Level Model KLM interface evaluation methodology"
        "natural language query interfaces SPARQL generation"
        "Retrieval Augmented Generation RAG document processing"
        "FastAPI microservices architecture building data"
        "Kafka integration real-time data synchronization"
        "GraphDB triple store semantic web technologies"
        "Qdrant vector database semantic search"
        "Docling document processing PDF extraction"
        "human computer interaction evaluation methods"
        "conversational interfaces usability studies"
        "building information management systems"
        "linked building data LBD semantic technologies"
    )
    
    for topic in "${research_topics[@]}"; do
        execute_opencode "/research \"$topic\"" "Research: $topic"
    done
    
    execute_opencode "/status" "Post-research status check"
    wait_for_confirmation "Phase 2 completed. Ready to proceed with writing phase?"
    
    # Phase 3: Chapter Writing (Following thesis structure)
    log "=== PHASE 3: CHAPTER WRITING ==="
    
    # Chapter 1: Introduction and Problem Statement
    log "--- Writing Chapter 1: Introduction and Problem Statement ---"
    execute_opencode "/write \"Chapter 1: Introduction and Problem Statement\"" "Write Chapter 1"
    execute_opencode "/review \"Chapter 1\"" "Review Chapter 1"
    execute_opencode "/compile" "Compile after Chapter 1"
    
    # Chapter 2: Fundamentals and Related Work
    log "--- Writing Chapter 2: Fundamentals and Related Work ---"
    execute_opencode "/write \"Chapter 2: Fundamentals and Related Work\"" "Write Chapter 2"
    execute_opencode "/review \"Chapter 2\"" "Review Chapter 2"
    execute_opencode "/compile" "Compile after Chapter 2"
    
    # Chapter 3: System Design and Architecture
    log "--- Writing Chapter 3: System Design and Architecture ---"
    execute_opencode "/write \"Chapter 3: System Design and Architecture\"" "Write Chapter 3"
    execute_opencode "/review \"Chapter 3\"" "Review Chapter 3"
    execute_opencode "/compile" "Compile after Chapter 3"
    
    wait_for_confirmation "First 3 chapters completed. Ready to continue with implementation chapters?"
    
    # Chapter 4: Implementation
    log "--- Writing Chapter 4: Implementation ---"
    execute_opencode "/write \"Chapter 4: Implementation\"" "Write Chapter 4"
    execute_opencode "/review \"Chapter 4\"" "Review Chapter 4"
    execute_opencode "/compile" "Compile after Chapter 4"
    
    # Chapter 5: Evaluation and Usability Study
    log "--- Writing Chapter 5: Evaluation and Usability Study ---"
    execute_opencode "/write \"Chapter 5: Evaluation and Usability Study\"" "Write Chapter 5"
    execute_opencode "/review \"Chapter 5\"" "Review Chapter 5"
    execute_opencode "/compile" "Compile after Chapter 5"
    
    # Chapter 6: Results and Discussion
    log "--- Writing Chapter 6: Results and Discussion ---"
    execute_opencode "/write \"Chapter 6: Results and Discussion\"" "Write Chapter 6"
    execute_opencode "/review \"Chapter 6\"" "Review Chapter 6"
    execute_opencode "/compile" "Compile after Chapter 6"
    
    # Chapter 7: Conclusion and Outlook
    log "--- Writing Chapter 7: Conclusion and Outlook ---"
    execute_opencode "/write \"Chapter 7: Conclusion and Outlook\"" "Write Chapter 7"
    execute_opencode "/review \"Chapter 7\"" "Review Chapter 7"
    execute_opencode "/compile" "Compile after Chapter 7"
    
    wait_for_confirmation "All chapters written. Ready to proceed with quality assurance phase?"
    
    # Phase 4: Quality Assurance and Final Review
    log "=== PHASE 4: QUALITY ASSURANCE ==="
    
    execute_opencode "/review \"entire thesis\"" "Comprehensive thesis review"
    execute_opencode "/references \"validate all citations and check duplicates\"" "Reference validation"
    execute_opencode "/references \"format bibliography according to academic standards\"" "Bibliography formatting"
    execute_opencode "/compile" "Full thesis compilation"
    
    # Phase 5: Final Status and Completion
    log "=== PHASE 5: FINAL STATUS AND COMPLETION ==="
    
    execute_opencode "/status" "Final thesis status check"
    execute_opencode "/compile" "Final compilation"
    
    log_success "=== THESIS WRITING PROCESS COMPLETED ==="
    log "Complete log available at: $LOG_FILE"
    log "Thesis document location: $THESIS_DIR/thesis/writing/thesis.tex"
    log "PDF output should be available at: $THESIS_DIR/thesis/writing/thesis.pdf"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --interactive     Run in interactive mode with confirmation prompts"
    echo "  --resume PHASE    Resume from specific phase (1-5)"
    echo "  --chapter N       Write only specific chapter (1-7)"
    echo "  --research-only   Run only the research phase"
    echo "  --help           Show this help message"
    echo ""
    echo "Phases:"
    echo "  1 - Initial Setup and Status Check"
    echo "  2 - Research Foundation"
    echo "  3 - Chapter Writing"
    echo "  4 - Quality Assurance"
    echo "  5 - Final Status and Completion"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run complete thesis writing process"
    echo "  $0 --interactive      # Run with confirmation prompts"
    echo "  $0 --resume 3         # Resume from chapter writing phase"
    echo "  $0 --chapter 2        # Write only Chapter 2"
    echo "  $0 --research-only    # Run only research phase"
}

# Function to write specific chapter
write_chapter() {
    local chapter_num="$1"
    local chapter_titles=(
        ""  # Index 0 unused
        "Chapter 1: Introduction and Problem Statement"
        "Chapter 2: Fundamentals and Related Work"
        "Chapter 3: System Design and Architecture"
        "Chapter 4: Implementation"
        "Chapter 5: Evaluation and Usability Study"
        "Chapter 6: Results and Discussion"
        "Chapter 7: Conclusion and Outlook"
    )

    if [[ $chapter_num -lt 1 || $chapter_num -gt 7 ]]; then
        log_error "Invalid chapter number: $chapter_num. Must be 1-7."
        exit 1
    fi

    local chapter_title="${chapter_titles[$chapter_num]}"
    log "=== WRITING SINGLE CHAPTER: $chapter_title ==="

    execute_opencode "/write \"$chapter_title\"" "Write $chapter_title"
    execute_opencode "/review \"Chapter $chapter_num\"" "Review Chapter $chapter_num"
    execute_opencode "/compile" "Compile after Chapter $chapter_num"

    log_success "Chapter $chapter_num completed successfully"
}

# Function to run only research phase
research_only() {
    log "=== RESEARCH-ONLY MODE ==="

    execute_opencode "/status" "Initial status check"

    # Core research topics
    research_topics=(
        "conversational AI interfaces for technical domains"
        "Building Information Modeling BIM graph databases RDF"
        "Large Language Models tool augmented agents PydanticAI"
        "Keystroke Level Model KLM interface evaluation methodology"
        "natural language query interfaces SPARQL generation"
        "Retrieval Augmented Generation RAG document processing"
        "FastAPI microservices architecture building data"
        "Kafka integration real-time data synchronization"
        "GraphDB triple store semantic web technologies"
        "Qdrant vector database semantic search"
        "Docling document processing PDF extraction"
        "human computer interaction evaluation methods"
        "conversational interfaces usability studies"
        "building information management systems"
        "linked building data LBD semantic technologies"
    )

    for topic in "${research_topics[@]}"; do
        execute_opencode "/research \"$topic\"" "Research: $topic"
    done

    execute_opencode "/status" "Post-research status check"
    log_success "Research phase completed successfully"
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Parse command line arguments
    INTERACTIVE=false
    RESUME_PHASE=""
    CHAPTER_ONLY=""
    RESEARCH_ONLY=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --interactive)
                INTERACTIVE=true
                shift
                ;;
            --resume)
                RESUME_PHASE="$2"
                shift 2
                ;;
            --chapter)
                CHAPTER_ONLY="$2"
                shift 2
                ;;
            --research-only)
                RESEARCH_ONLY=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    export INTERACTIVE

    # Trap to handle interruption
    trap 'log_error "Script interrupted by user"; exit 1' INT

    # Execute based on options
    if [[ "$RESEARCH_ONLY" == "true" ]]; then
        research_only
    elif [[ -n "$CHAPTER_ONLY" ]]; then
        write_chapter "$CHAPTER_ONLY"
    elif [[ -n "$RESUME_PHASE" ]]; then
        log "Resume functionality not yet implemented. Running full process."
        main
    else
        main
    fi
fi
