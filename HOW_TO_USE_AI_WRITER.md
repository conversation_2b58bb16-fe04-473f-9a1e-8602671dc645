# HOW TO USE AI WRITER - ConversationalBIM Thesis Assistant

This guide provides step-by-step instructions for using the AI-powered thesis writing system created for your ConversationalBIM bachelor thesis. The system includes multiple specialized AI agents that work together to help you research, write, review, and format your thesis efficiently.

## 📋 Table of Contents

1. [System Overview](#system-overview)
2. [Getting Started](#getting-started)
3. [Core Workflow](#core-workflow)
4. [Detailed Command Guide](#detailed-command-guide)
5. [Agent Interaction Guide](#agent-interaction-guide)
6. [Best Practices](#best-practices)
7. [Troubleshooting](#troubleshooting)
8. [Advanced Usage](#advanced-usage)

---

## 🔍 System Overview

### What You Have

Your AI writing system consists of:

- **4 Primary Agents**: Main assistants you interact with directly
- **4 Sub-agents**: Specialized assistants for specific tasks
- **6 Custom Commands**: Workflow shortcuts for common tasks
- **Integrated Research Tools**: arXiv, Google Scholar, and web search capabilities
- **Quality Assurance**: Multi-level review and consistency checking

### Key Features

- ✅ **Academic Research**: Automatic literature search and analysis
- ✅ **Professional Writing**: LaTeX-formatted academic content creation
- ✅ **Quality Control**: Multi-agent review and consistency checking
- ✅ **Citation Management**: Automatic bibliography and reference management
- ✅ **Progress Tracking**: Real-time thesis progress monitoring

---

## 🚀 Getting Started

### Step 1: Navigate to Your Project

```bash
cd /home/<USER>/dev/ai/bachelorarbeit
```

### Step 2: Launch OpenCode

```bash
opencode
```

### Step 3: Check System Status

Your first command should always be:

```
/status
```

This gives you an overview of your thesis progress and identifies what needs attention.

### Step 4: Understand Your Agents

- Press **Tab** to cycle through primary agents
- Use **@agent-name** to invoke specific sub-agents
- Use **/** followed by command names for workflows

---

## 📊 Core Workflow

### The Recommended Thesis Writing Process

#### Phase 1: Research Foundation

1. **Start with broad research**:

   ```
   /research "conversational AI interfaces for technical domains"
   ```

2. **Research specific topics**:

   ```
   /research "Building Information Modeling BIM graph databases"
   /research "Keystroke Level Model KLM interface evaluation"
   /research "Large Language Models tool augmented agents"
   ```

3. **Check research progress**:
   ```
   /status
   ```

#### Phase 2: Content Creation

1. **Write chapters in order**:

   ```
   /write "Chapter 1: Introduction and Problem Statement"
   /write "Chapter 2: Fundamentals and Related Work"
   /write "Chapter 3: System Design and Architecture"
   ```

2. **Review each chapter before moving on**:
   ```
   /review "Chapter 1"
   ```

#### Phase 3: Quality Assurance

1. **Comprehensive review**:

   ```
   /review "entire thesis"
   ```

2. **Manage references**:

   ```
   /references "validate all citations and check duplicates"
   ```

3. **Compile final document**:
   ```
   /compile
   ```

---

## 📚 Detailed Command Guide

### `/research [topic]` - Research Command

**Purpose**: Conduct comprehensive research on specific topics

**Usage Examples**:

```
/research "natural language interfaces graph databases"
/research "conversational AI building information modeling"
/research "SPARQL query generation LLM"
```

**What It Does**:

- Searches academic papers on arXiv and Google Scholar
- Finds technical documentation and implementation examples
- Creates detailed research reports with key findings
- Identifies research gaps relevant to your thesis
- Generates bibliography entries

**Expected Output**:

- Research report in `/thesis/.context/research/`
- Paper PDFs in `/thesis/.context/papers/`
- Bibliography entries ready for integration

---

### `/write [chapter/section]` - Writing Command

**Purpose**: Write complete chapters or sections in academic LaTeX format

**Usage Examples**:

```
/write "Chapter 1: Introduction and Problem Statement"
/write "Section 2.1: Building Information Modeling Fundamentals"
/write "Chapter 5: Evaluation Methodology"
```

**What It Does**:

- Reviews existing research materials
- Creates properly structured academic content
- Integrates citations and references
- Formats content in LaTeX
- Ensures academic writing standards

**Expected Output**:

- LaTeX-formatted chapter/section files in `/thesis/`
- Proper cross-references and citations
- Professional academic writing

---

### `/review [content]` - Review Command

**Purpose**: Comprehensive quality review of thesis content

**Usage Examples**:

```
/review "Chapter 1"
/review "entire thesis"
/review "Section 3.2 system architecture"
```

**What It Does**:

- Analyzes content for academic writing quality
- Checks technical accuracy and consistency
- Validates citations and references
- Identifies improvement opportunities
- Provides detailed feedback

**Expected Output**:

- Detailed review report with specific feedback
- Issue categorization (critical, important, minor)
- Improvement recommendations
- Quality rating assessment

---

### `/compile` - LaTeX Compilation Command

**Purpose**: Compile thesis document with professional formatting

**Usage Examples**:

```
/compile
/compile "check for errors only"
```

**What It Does**:

- Runs full LaTeX compilation sequence
- Processes bibliography with BibTeX/Biber
- Handles compilation errors and warnings
- Generates final PDF output
- Validates document structure

**Expected Output**:

- Compiled PDF thesis document
- Compilation log with any issues
- Error resolution guidance if needed

---

### `/references [task]` - Reference Management Command

**Purpose**: Manage citations, bibliography, and reference integrity

**Usage Examples**:

```
/references "add new citations from latest research"
/references "validate all citations and check duplicates"
/references "format bibliography according to IEEE style"
```

**What It Does**:

- Updates and validates BibTeX database
- Checks citation consistency throughout thesis
- Resolves duplicate references
- Verifies source accessibility
- Creates archive links for web resources

**Expected Output**:

- Updated bibliography database
- Reference validation report
- Citation consistency analysis

---

### `/status` - Progress Tracking Command

**Purpose**: Monitor thesis progress and identify next steps

**Usage Examples**:

```
/status
/status "detailed chapter analysis"
```

**What It Does**:

- Reviews completion status of all chapters
- Assesses research coverage and quality
- Identifies gaps and missing content
- Prioritizes next steps
- Tracks progress against deadlines

**Expected Output**:

- Comprehensive progress report
- Chapter completion percentages
- Priority task list
- Timeline analysis
- Risk assessment

---

## 🤖 Agent Interaction Guide

### Primary Agents (Use Tab to cycle through)

#### `thesis-coordinator` (Default)

- **Role**: Project orchestration and overall quality management
- **Use For**: Project planning, progress tracking, high-level decisions
- **Example**: "What should I focus on next for my thesis?"

#### `research`

- **Role**: Academic and technical research coordination
- **Use For**: Literature reviews, technical documentation research
- **Example**: "Find recent papers on conversational AI for technical domains"

#### `writer`

- **Role**: Content creation and academic writing
- **Use For**: Writing chapters, sections, and academic content
- **Example**: "Write the methodology section for KLM evaluation"

#### `reviewer`

- **Role**: Quality assurance and consistency checking
- **Use For**: Content review, style checking, academic standards validation
- **Example**: "Review Chapter 1 for academic writing quality"

### Sub-agents (Use @agent-name to invoke)

#### `@literature-research`

- **Specialty**: Academic paper discovery and analysis
- **Use For**: Finding specific papers, conducting literature reviews
- **Example**: "@literature-research find papers on natural language database interfaces"

#### `@technical-research`

- **Specialty**: Technical documentation and implementation research
- **Use For**: Framework research, implementation patterns, best practices
- **Example**: "@technical-research research PydanticAI implementation patterns"

#### `@reference-manager`

- **Specialty**: Citation and bibliography management
- **Use For**: Managing references, formatting citations, bibliography maintenance
- **Example**: "@reference-manager add these new papers to bibliography"

#### `@latex-formatter`

- **Specialty**: LaTeX formatting and document compilation
- **Use For**: Formatting issues, compilation problems, document structure
- **Example**: "@latex-formatter fix the table formatting in Chapter 4"

---

## ✨ Best Practices

### 1. Start Each Session with Status Check

Always begin with `/status` to understand current progress and priorities.

### 2. Research Before Writing

Use `/research` commands before writing any chapter to ensure you have comprehensive background material.

### 3. Write in Logical Order

Follow the thesis structure:

1. Introduction
2. Fundamentals/Related Work
3. System Design
4. Implementation
5. Evaluation
6. Results
7. Conclusion

### 4. Review Frequently

Use `/review` after completing each chapter to maintain quality standards.

### 5. Use Specific Command Arguments

Be specific in your commands:

- Good: `/write "Section 2.3: RDF Graph Database Technologies"`
- Less effective: `/write "background stuff"`

### 6. Leverage Sub-agents for Specialized Tasks

Use `@agent-name` for specialized tasks:

- `@literature-research` for academic papers
- `@technical-research` for implementation details
- `@reference-manager` for citation issues

### 7. Compile Regularly

Use `/compile` regularly to catch formatting issues early.

---

## 🔧 Troubleshooting

### Common Issues and Solutions

#### "Agent not responding as expected"

- **Solution**: Switch agents using Tab or specify with `@agent-name`
- **Check**: Make sure you're using the right agent for the task

#### "Research returning irrelevant results"

- **Solution**: Be more specific in research queries
- **Example**: Instead of "AI", use "conversational AI interfaces building information modeling"

#### "LaTeX compilation errors"

- **Solution**: Use `/compile` command which includes error handling
- **Alternative**: Ask `@latex-formatter` for specific formatting help

#### "Citations not working properly"

- **Solution**: Use `/references "validate citations"` command
- **Alternative**: Ask `@reference-manager` for citation-specific help

#### "Content quality concerns"

- **Solution**: Use `/review` command for comprehensive quality assessment
- **Follow-up**: Implement suggested improvements before proceeding

### Getting Help

- **General help**: Ask `thesis-coordinator` for guidance
- **Specific issues**: Use the appropriate sub-agent
- **Progress concerns**: Use `/status` for overview

---

## 🎯 Advanced Usage

### Combining Commands for Complex Workflows

#### Research → Write → Review Cycle

```bash
# 1. Research the topic
/research "Keystroke Level Model interface evaluation methodology"

# 2. Write based on research
/write "Section 5.1: Evaluation Methodology Design"

# 3. Review the written content
/review "Section 5.1"

# 4. Revise based on feedback (repeat as needed)
```

#### Multi-agent Collaboration

```bash
# Use multiple agents for complex tasks
@literature-research "find papers on conversational BIM interfaces"
@technical-research "research GraphDB SPARQL optimization techniques"
@writer "integrate findings into Chapter 2 literature review"
@reviewer "check consistency with Chapter 1 problem statement"
```

### Custom Workflows

#### Daily Writing Session

1. `/status` - Check progress
2. `/research [today's topic]` - Research if needed
3. `/write [specific section]` - Create content
4. `/review [what you wrote]` - Quality check
5. `/compile` - Generate PDF to see results

#### Pre-submission Checklist

1. `/review "entire thesis"` - Comprehensive review
2. `/references "final validation"` - Citation check
3. `/compile` - Final compilation
4. `/status` - Final progress check

---

## 📁 File Organization

Your system automatically organizes files:

```
thesis/
├── chapters/           # LaTeX chapter files
├── figures/           # Images and diagrams
├── references/        # Bibliography files
└── .context/
    ├── research/      # Research reports
    ├── papers/        # Downloaded PDFs
    ├── outlines/      # Chapter outlines
    ├── reviews/       # Review reports
    ├── references/    # Citation management
    └── notes/         # General notes
```

---

## 🎓 Success Tips

### For Efficient Thesis Writing:

1. **Be Consistent**: Use the system daily for best results
2. **Be Specific**: Detailed requests get better responses
3. **Follow the Process**: Research → Write → Review → Compile
4. **Use Progress Tracking**: Regular `/status` checks keep you on track
5. **Leverage Specialization**: Use sub-agents for their expertise areas

### For High-Quality Output:

1. **Academic Standards**: The system enforces proper academic writing
2. **Citation Integrity**: All sources are properly managed and cited
3. **Technical Accuracy**: Multiple validation layers ensure correctness
4. **Professional Presentation**: LaTeX formatting ensures publication quality

---

## 📞 Quick Reference

### Essential Commands:

- `/status` - Check progress
- `/research [topic]` - Research topics
- `/write [section]` - Write content
- `/review [content]` - Quality review
- `/compile` - Generate PDF
- `/references [task]` - Manage citations

### Key Agents:

- **thesis-coordinator** - Overall management
- **research** - Research tasks
- **writer** - Content creation
- **reviewer** - Quality control
- **@literature-research** - Academic papers
- **@technical-research** - Technical docs
- **@reference-manager** - Citations
- **@latex-formatter** - Formatting

---

This AI-powered thesis writing system is designed to help you efficiently produce a high-quality Computer Science bachelor thesis while maintaining rigorous academic standards. The system handles the complexity of research, writing, and formatting, allowing you to focus on your ideas and contributions.

**Remember**: The system is most effective when you provide clear, specific instructions and follow the recommended workflows. Start with `/status` and let the AI guide you through your thesis completion journey!
